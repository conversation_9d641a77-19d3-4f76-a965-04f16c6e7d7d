from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
import uuid
import random
import string
import json
from flask import current_app

db = SQLAlchemy()

# 用户角色关联表（多对多关系）
user_roles = db.Table('user_roles',
    db.Column('user_id', db.Integer, db.<PERSON>('users.id'), primary_key=True),
    db.Column('role_id', db.Integer, db.<PERSON>ey('roles.id'), primary_key=True),
    # 添加额外的索引
    db.Index('idx_user_roles_user_id', 'user_id'),
    db.Index('idx_user_roles_role_id', 'role_id'),
)

class Role(db.Model):
    """角色模型"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, index=True)
    display_name = db.Column(db.String(128))
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    users = db.relationship('User', secondary=user_roles, backref=db.backref('roles', lazy='dynamic'))
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Role {self.name}>'

class PasswordHistory(db.Model):
    """密码历史记录"""
    __tablename__ = 'password_history'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    password_hash = db.Column(db.String(128))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<PasswordHistory {self.user_id}:{self.created_at}>'

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True)
    email = db.Column(db.String(120), unique=True, index=True)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.Boolean, default=False)  # 保留兼容旧代码
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 安全相关字段
    login_attempts = db.Column(db.Integer, default=0)  # 登录失败次数
    locked_until = db.Column(db.DateTime, nullable=True)  # 账户锁定时间
    password_changed_at = db.Column(db.DateTime, default=datetime.utcnow)  # 密码最后修改时间
    force_password_change = db.Column(db.Boolean, default=False)  # 是否强制修改密码
    
    # 关系
    created_codes = db.relationship('InviteCode', foreign_keys='InviteCode.creator_id', backref='creator', lazy='dynamic')
    used_codes = db.relationship('InviteCode', foreign_keys='InviteCode.used_by_id', backref='user', lazy='dynamic')
    password_history = db.relationship('PasswordHistory', backref='user', lazy='dynamic',
                                      cascade='all, delete-orphan')
    
    @property
    def password(self):
        raise AttributeError('密码不可读')
        
    @password.setter
    def password(self, password):
        # 保存当前密码到历史记录
        if self.password_hash:
            history = PasswordHistory(user_id=self.id, password_hash=self.password_hash)
            db.session.add(history)
            
            # 限制历史记录数量
            if current_app and current_app.config.get('ADMIN_PASSWORD_HISTORY_COUNT'):
                history_limit = current_app.config['ADMIN_PASSWORD_HISTORY_COUNT']
                old_records = self.password_history.order_by(PasswordHistory.created_at.desc())\
                    .offset(history_limit).all()
                for record in old_records:
                    db.session.delete(record)
        
        self.password_hash = generate_password_hash(password)
        self.password_changed_at = datetime.utcnow()
        self.force_password_change = False
        
    def verify_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def check_password_history(self, password):
        """检查密码是否在历史记录中"""
        for history in self.password_history.all():
            if check_password_hash(history.password_hash, password):
                return True
        return False
    
    def is_password_expired(self):
        """检查密码是否过期"""
        if not current_app:
            return False
            
        expiry_days = current_app.config.get('ADMIN_PASSWORD_EXPIRY_DAYS')
        if not expiry_days or not self.has_role('admin'):
            return False
            
        expiry_date = self.password_changed_at + timedelta(days=expiry_days)
        return datetime.utcnow() > expiry_date
    
    def increment_login_attempts(self):
        """增加登录尝试次数并检查是否需要锁定账户"""
        self.login_attempts += 1
        
        if current_app and self.login_attempts >= current_app.config.get('ADMIN_MAX_LOGIN_ATTEMPTS', 5):
            lockout_minutes = current_app.config.get('ADMIN_ACCOUNT_LOCKOUT_MINUTES', 30)
            self.locked_until = datetime.utcnow() + timedelta(minutes=lockout_minutes)
            
        db.session.commit()
    
    def reset_login_attempts(self):
        """重置登录尝试次数"""
        self.login_attempts = 0
        self.locked_until = None
        db.session.commit()
    
    def is_locked(self):
        """检查账户是否被锁定"""
        if not self.locked_until:
            return False
        return datetime.utcnow() < self.locked_until
    
    def add_role(self, role_name):
        """添加角色"""
        role = Role.query.filter_by(name=role_name).first()
        if role and role not in self.roles:
            self.roles.append(role)
            return True
        return False
    
    def remove_role(self, role_name):
        """移除角色"""
        role = Role.query.filter_by(name=role_name).first()
        if role and role in self.roles:
            self.roles.remove(role)
            return True
        return False
    
    def has_role(self, role_name):
        """检查是否有指定角色"""
        role = Role.query.filter_by(name=role_name).first()
        return role in self.roles if role else False
    
    @staticmethod
    def get_users_with_roles(page=1, per_page=20):
        """获取用户列表并预加载角色数据"""
        from sqlalchemy.orm import joinedload
        
        pagination = User.query.options(
            joinedload(User.roles)
        ).order_by(User.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return pagination
    
    def to_dict(self, include_roles=True):
        """转换为字典格式，优化角色数据获取"""
        result = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_admin': self.is_admin,
            'created_at': self.created_at.isoformat(),
            'password_changed_at': self.password_changed_at.isoformat(),
            'force_password_change': self.force_password_change,
            'account_locked': self.is_locked()
        }
        
        if include_roles:
            # 简化逻辑，直接获取角色数据
            roles_list = [{'id': role.id, 'name': role.name, 'display_name': role.display_name} 
                         for role in self.roles]
            result['roles'] = roles_list
        
        return result
    
    def __repr__(self):
        return f'<User {self.username}>'


class ApiKey(db.Model):
    """API密钥模型"""
    __tablename__ = 'api_keys'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(32), unique=True, index=True)
    name = db.Column(db.String(128))  # API Key名称/描述
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_used_at = db.Column(db.DateTime, nullable=True)
    
    # 关系
    user = db.relationship('User', backref=db.backref('api_keys', lazy='dynamic'))
    invite_codes = db.relationship('InviteCode', backref='api_key', lazy='dynamic')
    
    @staticmethod
    def generate_key():
        """生成API密钥"""
        # 格式：ag-xxxxxxxxxxxxxxxx (ag- + 16位随机字符)
        chars = string.ascii_lowercase + string.digits
        random_part = ''.join(random.choice(chars) for _ in range(16))
        return f'ag-{random_part}'
    
    @staticmethod
    def create_key(user_id, name):
        """创建新的API密钥"""
        key = ApiKey.generate_key()
        while ApiKey.query.filter_by(key=key).first():
            key = ApiKey.generate_key()
        
        api_key = ApiKey(
            key=key,
            name=name,
            user_id=user_id
        )
        return api_key
    
    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used_at = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        # 优化：检查是否已经有缓存的计数值
        if hasattr(self, '_invite_codes_count'):
            invite_codes_count = self._invite_codes_count
        else:
            # 使用更高效的计数方式
            invite_codes_count = db.session.query(InviteCode).filter_by(api_key_id=self.id).count()
        
        return {
            'id': self.id,
            'key': self.key,
            'name': self.name,
            'user_id': self.user_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None,
            'invite_codes_count': invite_codes_count
        }
    
    @staticmethod
    def get_api_keys_with_counts(user_id, page=1, per_page=10):
        """获取API Key列表并预加载邀请码计数"""
        from sqlalchemy import func
        
        # 分页查询API Key
        pagination = ApiKey.query.filter_by(user_id=user_id).order_by(
            ApiKey.created_at.desc()
        ).paginate(page=page, per_page=per_page, error_out=False)
        
        # 获取当前页的API Key ID列表
        api_key_ids = [key.id for key in pagination.items]
        
        if api_key_ids:
            # 批量查询邀请码计数
            counts_query = db.session.query(
                InviteCode.api_key_id,
                func.count(InviteCode.id).label('count')
            ).filter(
                InviteCode.api_key_id.in_(api_key_ids)
            ).group_by(InviteCode.api_key_id).all()
            
            # 创建计数映射
            counts_map = {row.api_key_id: row.count for row in counts_query}
            
            # 为每个API Key设置计数
            for api_key in pagination.items:
                api_key._invite_codes_count = counts_map.get(api_key.id, 0)
        
        return pagination
    
    def __repr__(self):
        return f'<ApiKey {self.key}>'


class InviteCode(db.Model):
    """邀请码模型"""
    __tablename__ = 'invite_codes'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(8), unique=True, index=True)
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'), index=True)
    api_key_id = db.Column(db.Integer, db.ForeignKey('api_keys.id'), nullable=True, index=True)  # 关联API密钥
    used_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_used = db.Column(db.Boolean, default=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    expires_at = db.Column(db.DateTime, index=True)
    redeemed_at = db.Column(db.DateTime, nullable=True)
    
    # 添加复合索引
    __table_args__ = (
        db.Index('idx_api_key_status_created', 'api_key_id', 'is_used', 'created_at'),
        db.Index('idx_creator_status_expires', 'creator_id', 'is_used', 'expires_at'),
        db.Index('idx_status_expires', 'is_used', 'expires_at'),
    )
    
    @staticmethod
    def generate_code(length=8):
        """生成随机邀请码"""
        # 使用字母和数字，排除容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('O', '').replace('0', '').replace('I', '').replace('1', '')
        return ''.join(random.choice(chars) for _ in range(length))
    
    @staticmethod
    def create_code(creator_id, expiry_days=7, api_key_id=None):
        """创建新邀请码"""
        code = InviteCode.generate_code()
        while InviteCode.query.filter_by(code=code).first():
            code = InviteCode.generate_code()
        
        invite = InviteCode(
            code=code,
            creator_id=creator_id,
            api_key_id=api_key_id,
            expires_at=datetime.utcnow() + timedelta(days=expiry_days)
        )
        return invite
    
    def is_valid(self):
        """检查邀请码是否有效"""
        return not self.is_used and datetime.utcnow() <= self.expires_at
    
    def redeem(self, user_id):
        """核销邀请码"""
        if not self.is_valid():
            return False
        
        self.is_used = True
        self.used_by_id = user_id
        self.redeemed_at = datetime.utcnow()
        return True
    
    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'creator_id': self.creator_id,
            'api_key_id': self.api_key_id,
            'used_by_id': self.used_by_id,
            'is_used': self.is_used,
            'created_at': self.created_at.isoformat(),
            'expires_at': self.expires_at.isoformat(),
            'redeemed_at': self.redeemed_at.isoformat() if self.redeemed_at else None,
            'is_valid': self.is_valid(),
            'api_key_name': getattr(self, '_api_key_name', None)
        }
    
    def __repr__(self):
        return f'<InviteCode {self.code}>' 