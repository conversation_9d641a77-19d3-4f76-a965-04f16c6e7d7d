/* 现代化控制台样式 - 黑白配色现代风格 */
:root {
    /* 主色调系统 - 现代黑白 */
    --color-primary: #000000;
    --color-secondary: #1a1a1a;
    --color-tertiary: #2d2d2d;
    --color-quaternary: #404040;
    --color-accent: #666666;

    /* 背景色系统 */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    --bg-dark: #000000;
    --bg-dark-secondary: #1a1a1a;

    /* 边框色系统 */
    --border-light: #e5e5e5;
    --border-medium: #d0d0d0;
    --border-dark: #000000;

    /* 文字色系统 */
    --text-primary: #000000;
    --text-secondary: #404040;
    --text-tertiary: #666666;
    --text-muted: #999999;
    --text-inverse: #ffffff;

    /* 状态颜色 - 现代化 */
    --color-success: #000000;
    --color-warning: #404040;
    --color-danger: #000000;
    --color-info: #666666;

    /* 阴影系统 - 现代化微阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.12);

    /* 边框半径 - 现代化 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    --radius-full: 9999px;

    /* 间距系统 - 现代化 */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 12px;
    --space-lg: 16px;
    --space-xl: 20px;
    --space-2xl: 24px;
    --space-3xl: 32px;
    --space-4xl: 40px;

    /* 字体系统 */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

    /* 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;

    /* 动画系统 - 现代化 */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 布局 */
    --sidebar-width: 280px;
    --header-height: 64px;
    --content-max-width: 1400px;
}

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    font-size: var(--text-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* 现代化滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--color-accent);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-quaternary);
}

/* 主布局 */
.dashboard {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

/* 现代化侧边栏 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-dark);
    color: var(--text-inverse);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform var(--transition-base);
    border-right: 1px solid var(--border-medium);
    backdrop-filter: blur(10px);
}

.sidebar-header {
    padding: var(--space-2xl) var(--space-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
}

.sidebar-header h2 {
    font-size: var(--text-xl);
    font-weight: 800;
    margin-bottom: var(--space-sm);
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-header p {
    font-size: var(--text-sm);
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* 现代化导航样式 */
.sidebar-nav {
    padding: var(--space-xl) 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0 var(--space-lg);
}

.sidebar-nav li {
    margin-bottom: var(--space-sm);
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--text-sm);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    position: relative;
    margin: var(--space-xs) 0;
    overflow: hidden;
}

.sidebar-nav .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    transform: scaleY(0);
    transition: transform var(--transition-base);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.sidebar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.08);
    color: rgba(255, 255, 255, 0.9);
    transform: translateX(4px);
}

.sidebar-nav .nav-link.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    color: var(--text-inverse);
    font-weight: 600;
    box-shadow: var(--shadow-md);
}

.sidebar-nav .nav-link.active::before {
    transform: scaleY(1);
}

/* 现代化侧边栏图标 */
.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--space-md);
    opacity: 0.7;
    transition: all var(--transition-base);
    stroke-width: 1.5;
}

.nav-link:hover .nav-icon {
    opacity: 0.9;
    transform: scale(1.1);
}

.nav-link.active .nav-icon {
    opacity: 1;
    transform: scale(1.05);
}

/* 现代化侧边栏底部 */
.sidebar-footer {
    padding: var(--space-xl);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    margin-top: auto;
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
}

.sidebar-footer p {
    font-size: var(--text-xs);
    color: rgba(255, 255, 255, 0.4);
    margin: 0;
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* 现代化主内容区域 */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: var(--bg-secondary);
    position: relative;
}

/* 现代化内容头部 */
.content-header {
    background: var(--bg-primary);
    padding: var(--space-xl) var(--space-2xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.content-header h1 {
    font-size: var(--text-2xl);
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.02em;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    color: var(--text-secondary);
}

.user-info span {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
}

/* 现代化内容主体 */
.content-body {
    padding: var(--space-2xl);
    max-width: var(--content-max-width);
    margin: 0 auto;
}

/* 现代化卡片样式 */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--space-2xl);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-xl) var(--space-2xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.card-header h2 {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
}

.card-body {
    padding: var(--space-2xl);
}

/* 现代化统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-3xl);
}

/* 现代化统计卡片 */
.stat-card {
    background: var(--bg-primary);
    padding: var(--space-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.stat-card h3 {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.stat-card .stat-value {
    font-size: var(--text-4xl);
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: var(--space-lg);
    letter-spacing: -0.02em;
    line-height: 1;
}

.stat-card .stat-label {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    line-height: 1.4;
    font-weight: 500;
}

/* 现代化按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-md) var(--space-xl);
    font-size: var(--text-sm);
    font-weight: 600;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    text-decoration: none;
    cursor: pointer;
    gap: var(--space-sm);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
    letter-spacing: 0.02em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    color: var(--text-inverse);
    border: 1px solid var(--color-primary);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.btn-secondary:hover {
    background: var(--border-medium);
    transform: translateY(-1px);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-dark);
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: var(--text-inverse);
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--text-base);
}

/* 现代化表单样式 */
.form-group {
    margin-bottom: var(--space-xl);
}

.form-group label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: 0.02em;
}

.form-control {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    background-color: var(--bg-primary);
    transition: all var(--transition-base);
    font-family: var(--font-sans);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.form-control:hover {
    border-color: var(--border-medium);
}

.form-actions {
    display: flex;
    gap: var(--space-lg);
    justify-content: flex-start;
    margin-top: var(--space-2xl);
    flex-wrap: wrap;
}

/* 现代化表格样式 */
.table-container {
    overflow-x: auto;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    background: var(--bg-primary);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
}

.table th,
.table td {
    padding: var(--space-lg) var(--space-xl);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
    font-size: var(--text-sm);
    transition: background-color var(--transition-fast);
}

.table th {
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    font-weight: 700;
    color: var(--text-inverse);
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: var(--space-xl);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: all var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--bg-secondary);
    transform: scale(1.001);
}

.table tbody tr:nth-child(even) {
    background-color: var(--bg-tertiary);
}

.table tbody tr:nth-child(even):hover {
    background-color: var(--bg-secondary);
}

/* 现代化徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-xs);
    font-weight: 700;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid transparent;
    transition: all var(--transition-fast);
}

.badge-success {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    border: 1px solid var(--border-dark);
}

.badge-warning {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.badge-danger {
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    color: var(--text-inverse);
    border: 1px solid var(--bg-dark);
}

.badge-info {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px dashed var(--border-medium);
}

.badge-dark {
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--color-secondary) 100%);
    color: var(--text-inverse);
}

/* 现代化分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-sm);
    margin: var(--space-2xl) 0;
}

.pagination .page-btn {
    padding: var(--space-md) var(--space-lg);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-base);
    min-width: 44px;
    text-align: center;
}

.pagination .page-btn:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-medium);
    transform: translateY(-1px);
}

.pagination .page-btn.active {
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    color: var(--text-inverse);
    border-color: var(--bg-dark);
    box-shadow: var(--shadow-md);
}

.pagination .page-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
}

.pagination .page-btn:disabled:hover {
    background-color: var(--bg-primary);
    border-color: var(--border-light);
    transform: none;
}

/* 现代化模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
    animation: modalFadeIn var(--transition-base) ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    max-width: 600px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    animation: modalSlideIn var(--transition-base) ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    padding: var(--space-2xl) var(--space-2xl) var(--space-xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: -0.02em;
}

.modal-close {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    font-size: var(--text-lg);
    cursor: pointer;
    color: var(--text-primary);
    padding: var(--space-sm);
    border-radius: var(--radius-lg);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--border-medium);
    transform: scale(1.1);
}

.modal-body {
    padding: var(--space-2xl);
}

/* 现代化加载动画 */
.loader {
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    animation: modernSpin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    margin: var(--space-xl) auto;
}

/* 现代化旋转器样式 */
.spinner-border {
    display: inline-block;
    width: 1.2rem;
    height: 1.2rem;
    vertical-align: text-bottom;
    border: 0.15em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: modernSpinner 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.125em;
}

/* 现代化动画关键帧 */
@keyframes modernSpin {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

@keyframes modernSpinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* 内容区域显示 */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* 现代化响应式设计 */
@media (max-width: 1200px) {
    :root {
        --sidebar-width: 260px;
        --content-max-width: 1200px;
    }

    .content-body {
        padding: var(--space-xl);
    }
}

@media (max-width: 1024px) {
    :root {
        --sidebar-width: 240px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-base);
        box-shadow: var(--shadow-xl);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-header {
        padding: var(--space-lg) var(--space-xl);
    }

    .content-header h1 {
        font-size: var(--text-xl);
    }

    .content-body {
        padding: var(--space-xl);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .card-header {
        padding: var(--space-xl);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-md);
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: var(--space-md) var(--space-lg);
    }

    .content-header h1 {
        font-size: var(--text-lg);
    }

    .content-body {
        padding: var(--space-lg);
    }

    .card-header,
    .card-body {
        padding: var(--space-lg);
    }

    .stat-card {
        padding: var(--space-xl);
    }

    .stat-card .stat-value {
        font-size: var(--text-3xl);
    }

    .modal-content {
        width: 95%;
        margin: var(--space-lg);
    }

    .modal-header,
    .modal-body {
        padding: var(--space-xl);
    }
}

/* 现代化侧边栏切换按钮 */
.sidebar-toggle {
    position: fixed;
    top: var(--space-xl);
    left: var(--space-xl);
    z-index: 1001;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    color: var(--text-inverse);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    cursor: pointer;
    display: none;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-base);
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

@media (max-width: 768px) {
    .sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 工具提示 */
/* 移除工具提示 */

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--space-3xl);
    color: var(--color-dark-gray);
}

.empty-state svg {
    width: 64px;
    height: 64px;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-sm);
    color: var(--color-primary);
}

.empty-state p {
    font-size: var(--text-sm);
    margin-bottom: var(--space-lg);
}

/* 成功/错误消息 */
.alert {
    padding: var(--space-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-lg);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--color-success);
    color: var(--color-success);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--color-warning);
    color: var(--color-warning);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: var(--color-danger);
    color: var(--color-danger);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: var(--color-info);
    color: var(--color-info);
} 

/* 批量操作工具栏 - 优化 */
.batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
    padding: var(--space-sm) var(--space-md);
    background-color: var(--color-light-gray);
    border-radius: var(--radius-sm);
    border: 1px solid var(--color-medium-gray);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.batch-info {
    margin-left: var(--space-md);
    color: var(--color-dark-gray);
    font-size: var(--text-xs);
}

/* 表格操作按钮 */
.table .actions {
    white-space: nowrap;
}

.table .actions .btn {
    margin-right: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    border-radius: var(--radius-sm);
}

.table .actions .btn:last-child {
    margin-right: 0;
}

/* 邀请码显示 */
.code-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    letter-spacing: 1px;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* 复选框样式 */
.code-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.code-checkbox:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 现代化提示消息动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
    to {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
    }
}

/* 现代化Toast样式 */
.toast {
    position: fixed;
    top: var(--space-2xl);
    right: var(--space-2xl);
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-xl);
    color: var(--text-inverse);
    font-weight: 600;
    z-index: 10000;
    box-shadow: var(--shadow-xl);
    max-width: 400px;
    word-wrap: break-word;
    backdrop-filter: blur(10px);
    animation: slideInRight var(--transition-base) ease-out;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.toast-success {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.toast-error {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-tertiary) 100%);
}

.toast-warning {
    background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-quaternary) 100%);
    color: var(--text-inverse);
}

.toast-info {
    background: linear-gradient(135deg, var(--color-info) 0%, var(--color-accent) 100%);
}

/* 卡片头部动作按钮 */
.card-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 按钮尺寸优化 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* 表格响应式优化 */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
    }
    
    .batch-toolbar {
        flex-direction: column;
        gap: 10px;
    }
    
    .batch-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .batch-info {
        margin-left: 0;
        text-align: center;
    }
}

/* 禁用状态的按钮样式 - 黑白风格 */
.btn:disabled {
    background-color: var(--color-light-gray);
    color: var(--color-dark-gray);
    border: 1px solid var(--color-dark-gray);
    cursor: not-allowed;
}

/* 加载状态优化 */
.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态徽章优化 */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: var(--color-white);
    background-color: var(--color-success);
}

.badge-warning {
    color: var(--color-black);
    background-color: var(--color-warning);
}

.badge-danger {
    color: var(--color-white);
    background-color: var(--color-danger);
} 

/* 批量生成邀请码样式 */
.form-row {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.form-row .form-group {
    flex: 1;
}

.form-text {
    font-size: var(--text-sm);
    color: var(--color-dark-gray);
    margin-top: var(--space-xs);
}

.batch-result {
    margin-top: var(--space-lg);
    padding: var(--space-lg);
    background-color: var(--color-white);
    border-radius: 0;
    border: 1px solid var(--color-black);
}

.batch-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 1px solid var(--color-black);
}

.batch-result-header h4 {
    margin: 0;
    color: var(--color-black);
    font-size: var(--text-lg);
    font-weight: 600;
}

.batch-result-actions {
    display: flex;
    gap: var(--space-sm);
}

.batch-result-summary {
    margin-bottom: var(--space-md);
    padding: var(--space-sm);
    background-color: var(--color-black);
    color: var(--color-white);
    border-radius: 0;
    font-weight: 600;
}

.batch-codes-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--color-black);
    border-radius: 0;
    background-color: var(--color-white);
}

.batch-codes-list {
    padding: var(--space-sm);
}

.batch-code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm);
    margin-bottom: var(--space-xs);
    background-color: var(--color-white);
    border-radius: 0;
    border: 1px solid var(--color-black);
}

.batch-code-item:last-child {
    margin-bottom: 0;
}

.batch-code-text {
    font-family: var(--font-mono);
    font-weight: 600;
    letter-spacing: 1px;
    color: var(--color-black);
}

.batch-code-item .btn {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
}

/* 加载状态样式 */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125em;
}

@keyframes spinner-border {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: var(--space-sm);
    }
    
    .batch-result-header {
        flex-direction: column;
        gap: var(--space-sm);
        align-items: flex-start;
    }
    
    .batch-result-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .batch-code-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-xs);
    }
    
    .batch-code-item .btn {
        align-self: flex-end;
    }
}

/* 按钮outline样式 */
.btn-outline-primary {
    color: var(--color-primary);
    border-color: var(--color-primary);
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.btn-outline-primary:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.25);
}

/* 现代化工具类 */
.text-muted {
    color: var(--text-muted);
}

/* 现代化特效类 */
.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
    transition: transform var(--transition-base);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.fade-in {
    animation: fadeIn var(--transition-slow) ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 现代化焦点样式 */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* 现代化禁用状态 */
.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 现代化文本选择 */
::selection {
    background: var(--color-primary);
    color: var(--text-inverse);
}

/* 现代化滚动行为 */
html {
    scroll-behavior: smooth;
}

/* 现代化代码显示 */
.code-display {
    font-family: var(--font-mono);
    font-weight: 600;
    letter-spacing: 0.05em;
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    font-size: var(--text-sm);
}

/* 现代化复选框样式 */
.code-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--color-primary);
    border-radius: var(--radius-sm);
}

.code-checkbox:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 现代化增强样式 */

/* 卡片头部操作按钮组 */
.card-header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-wrap: wrap;
}

/* 现代化空状态 */
.empty-state {
    text-align: center;
    padding: var(--space-4xl);
    color: var(--text-tertiary);
}

.empty-state svg {
    width: 80px;
    height: 80px;
    margin-bottom: var(--space-xl);
    opacity: 0.3;
    stroke: var(--text-tertiary);
}

.empty-state h3 {
    font-size: var(--text-xl);
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
    font-weight: 600;
}

.empty-state p {
    font-size: var(--text-base);
    margin-bottom: var(--space-xl);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* 现代化警告框 */
.alert {
    padding: var(--space-xl);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-xl);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    backdrop-filter: blur(10px);
}

.alert-success {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
    border-color: var(--border-medium);
    color: var(--text-primary);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(64, 64, 64, 0.05) 0%, rgba(64, 64, 64, 0.02) 100%);
    border-color: var(--color-accent);
    color: var(--text-primary);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.04) 100%);
    border-color: var(--color-primary);
    color: var(--text-primary);
}

.alert-info {
    background: linear-gradient(135deg, rgba(102, 102, 102, 0.05) 0%, rgba(102, 102, 102, 0.02) 100%);
    border-color: var(--color-info);
    color: var(--text-primary);
}

/* 现代化批量操作工具栏 */
.batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    padding: var(--space-lg) var(--space-xl);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.batch-info {
    color: var(--text-tertiary);
    font-size: var(--text-sm);
    font-weight: 500;
}

/* 现代化表格操作按钮 */
.table .actions {
    white-space: nowrap;
}

.table .actions .btn {
    margin-right: var(--space-sm);
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-xs);
}

.table .actions .btn:last-child {
    margin-right: 0;
}

/* 现代化按钮禁用状态 */
.btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    border: 1px solid var(--border-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn:disabled::before {
    display: none;
}

/* 现代化表单行 */
.form-row {
    display: flex;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-text {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    margin-top: var(--space-sm);
    line-height: 1.4;
}

/* 现代化批量结果展示 */
.batch-result {
    margin-top: var(--space-2xl);
    padding: var(--space-2xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-md);
}

.batch-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
}

.batch-result-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--text-xl);
    font-weight: 700;
}

.batch-result-actions {
    display: flex;
    gap: var(--space-md);
}

.batch-result-summary {
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    color: var(--text-inverse);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-align: center;
}

.batch-codes-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
}

.batch-codes-list {
    padding: var(--space-lg);
}

.batch-code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
    margin-bottom: var(--space-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.batch-code-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
}

.batch-code-item:last-child {
    margin-bottom: 0;
}

.batch-code-text {
    font-family: var(--font-mono);
    font-weight: 700;
    letter-spacing: 0.1em;
    color: var(--text-primary);
    font-size: var(--text-base);
}

.batch-code-item .btn {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-xs);
}