/* 现代化控制台样式 - 纯黑白简约风格 */
:root {
    /* 颜色系统 - 纯黑白 */
    --color-primary: #000000;
    --color-secondary: #121212;
    --color-tertiary: #2a2a2a;
    --color-quaternary: #555555;
    --color-light-gray: #f5f5f5;
    --color-medium-gray: #e0e0e0;
    --color-dark-gray: #757575;
    --color-white: #ffffff;
    --color-black: #000000;
    
    /* 语义化颜色 - 黑白灰阶 */
    --color-success: #2e2e2e;
    --color-warning: #4a4a4a;
    --color-danger: #1a1a1a;
    --color-info: #3a3a3a;
    
    /* 阴影系统 - 扁平化 */
    --shadow-sm: 0 1px 1px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 1px 2px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 1px 4px rgba(0, 0, 0, 0.12);
    
    /* 边框半径 - 更小的圆角 */
    --radius-sm: 2px;
    --radius-md: 4px;
    --radius-lg: 6px;
    --radius-xl: 8px;
    --radius-full: 9999px;
    
    /* 间距系统 - 紧凑化 */
    --space-xs: 2px;
    --space-sm: 4px;
    --space-md: 8px;
    --space-lg: 12px;
    --space-xl: 16px;
    --space-2xl: 24px;
    --space-3xl: 32px;
    
    /* 字体系统 */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    
    /* 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* 动画 - 简化 */
    --transition-fast: 0.15s ease-in-out;
    --transition-base: 0.2s ease-in-out;
    --transition-slow: 0.3s ease-in-out;
    
    /* 布局 */
    --sidebar-width: 260px;
    --header-height: 56px;
}

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    color: var(--color-primary);
    background-color: var(--color-light-gray);
    font-size: var(--text-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--color-medium-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--color-dark-gray);
    border-radius: var(--radius-full);
}

/* 移除滚动条hover效果 */

/* 主布局 */
.dashboard {
    display: flex;
    min-height: 100vh;
    background-color: var(--color-light-gray);
}

/* 侧边栏 */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    color: var(--color-white);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform var(--transition-base);
}

.sidebar-header {
    padding: var(--space-lg) var(--space-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    font-size: var(--text-lg);
    font-weight: 700;
    margin-bottom: var(--space-xs);
    letter-spacing: -0.025em;
}

.sidebar-header p {
    font-size: var(--text-sm);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

/* 导航样式 - 简化 */
.sidebar-nav {
    padding: var(--space-md) 0;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: var(--space-xs);
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--text-sm);
    transition: background-color var(--transition-fast);
    position: relative;
}

.sidebar-nav .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: var(--color-white);
    transform: scaleY(0);
    transition: transform var(--transition-fast);
}

.sidebar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.12);
    color: var(--color-white);
    font-weight: 600;
    border-left: 3px solid var(--color-white);
}

/* 侧边栏图标 */
.nav-icon {
    width: 18px;
    height: 18px;
    margin-right: var(--space-sm);
    opacity: 0.8;
}

.nav-link.active .nav-icon {
    opacity: 1;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: var(--space-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
    text-align: center;
}

.sidebar-footer p {
    font-size: var(--text-xs);
    color: rgba(255, 255, 255, 0.5);
    margin: 0;
}

/* 主内容区域 */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background-color: var(--color-light-gray);
}

/* 内容头部 */
.content-header {
    background: var(--color-white);
    padding: var(--space-md) var(--space-xl);
    border-bottom: 1px solid var(--color-medium-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-header h1 {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--color-primary);
}

.user-info {
    display: flex;
    align-items: center;
    color: var(--color-dark-gray);
}

.user-info span {
    font-size: var(--text-sm);
    font-weight: 500;
}

/* 内容主体 - 紧凑化 */
.content-body {
    padding: var(--space-lg);
}

/* 卡片样式 - 扁平化黑白风格 */
.card {
    background: var(--color-white);
    border-radius: var(--radius-sm);
    box-shadow: none;
    margin-bottom: var(--space-lg);
    border: 1px solid var(--color-black);
}

.card-header {
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--color-black);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-light-gray);
}

.card-header h2 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--color-black);
    margin: 0;
    letter-spacing: -0.5px;
}

.card-body {
    padding: var(--space-lg);
}

/* 统计网格 - 紧凑化 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

/* 统计卡片 - 现代黑白风格 */
.stat-card {
    background: var(--color-white);
    padding: var(--space-xl);
    border-radius: var(--radius-sm);
    box-shadow: none;
    border: 1px solid var(--color-black);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stat-card h3 {
    font-size: var(--text-sm);
    font-weight: 700;
    color: var(--color-black);
    margin-bottom: var(--space-md);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-card .stat-value {
    font-size: var(--text-3xl);
    font-weight: 800;
    color: var(--color-black);
    margin-bottom: var(--space-md);
    letter-spacing: -1px;
}

.stat-card .stat-label {
    font-size: var(--text-xs);
    color: var(--color-quaternary);
    line-height: 1.4;
    font-weight: 500;
}

/* 按钮样式 - 扁平化无hover效果 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-sm);
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--radius-sm);
    text-decoration: none;
    cursor: pointer;
    gap: var(--space-xs);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--color-primary);
    color: var(--color-white);
    border: 1px solid var(--color-primary);
}

.btn-secondary {
    background-color: var(--color-medium-gray);
    color: var(--color-primary);
    border: 1px solid var(--color-medium-gray);
}

.btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
}

.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--text-base);
}

/* 表单样式 - 紧凑化 */
.form-group {
    margin-bottom: var(--space-md);
}

.form-group label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--color-primary);
    margin-bottom: var(--space-xs);
}

.form-control {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--color-medium-gray);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    background-color: var(--color-white);
    transition: border-color var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-black);
    box-shadow: none;
}

.form-actions {
    display: flex;
    gap: var(--space-sm);
    justify-content: flex-start;
    margin-top: var(--space-md);
}

/* 表格样式 - 现代黑白风格 */
.table-container {
    overflow-x: auto;
    border-radius: var(--radius-sm);
    border: 1px solid var(--color-black);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--color-white);
}

.table th,
.table td {
    padding: var(--space-md) var(--space-lg);
    text-align: left;
    border-bottom: 1px solid var(--color-black);
    font-size: var(--text-sm);
}

.table th {
    background-color: var(--color-black);
    font-weight: 600;
    color: var(--color-white);
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: var(--space-lg) var(--space-lg);
}

.table tbody tr:nth-child(even) {
    background-color: var(--color-light-gray);
}

/* 徽章样式 - 黑白风格 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-xs);
    font-weight: 600;
    border-radius: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid var(--color-black);
}

.badge-success {
    background-color: var(--color-white);
    color: var(--color-black);
    border: 1px solid var(--color-black);
}

.badge-warning {
    background-color: var(--color-light-gray);
    color: var(--color-black);
    border: 1px solid var(--color-black);
}

.badge-danger {
    background-color: var(--color-black);
    color: var(--color-white);
}

.badge-info {
    background-color: var(--color-white);
    color: var(--color-black);
    border-style: dashed;
}

.badge-dark {
    background-color: var(--color-black);
    color: var(--color-white);
}

/* 分页样式 - 黑白风格 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-xs);
    margin: var(--space-md) 0;
}

.pagination .page-btn {
    padding: var(--space-xs) var(--space-md);
    background-color: var(--color-white);
    border: 1px solid var(--color-black);
    color: var(--color-black);
    text-decoration: none;
    border-radius: 0;
    font-size: var(--text-sm);
    cursor: pointer;
}

.pagination .page-btn.active {
    background-color: var(--color-black);
    color: var(--color-white);
    border-color: var(--color-black);
}

.pagination .page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 模态框样式 - 简化 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.modal-content {
    background-color: var(--color-white);
    border-radius: 0;
    box-shadow: none;
    border: 1px solid var(--color-black);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--color-black);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-light-gray);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--color-black);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--text-xl);
    cursor: pointer;
    color: var(--color-black);
    padding: var(--space-xs);
    border-radius: 0;
}

.modal-body {
    padding: var(--space-lg);
}

/* 加载动画 - 黑白风格 */
.loader {
    border: 2px solid var(--color-light-gray);
    border-top: 2px solid var(--color-black);
    border-radius: 0;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    margin: var(--space-md) auto;
}

/* 旋转器样式 - 用于按钮等小组件 */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125em;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes spinner-border {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 内容区域显示 */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* 响应式设计 - 调整断点 */
@media (max-width: 1024px) {
    :root {
        --sidebar-width: 240px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-base);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-header {
        padding: var(--space-sm) var(--space-md);
    }

    .content-body {
        padding: var(--space-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .content-header h1 {
        font-size: var(--text-lg);
    }

    .card-header,
    .card-body {
        padding: var(--space-md);
    }

    .stat-card {
        padding: var(--space-sm);
    }

    .stat-card .stat-value {
        font-size: var(--text-xl);
    }
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
    position: fixed;
    top: var(--space-md);
    left: var(--space-md);
    z-index: 1001;
    background-color: var(--color-black);
    color: var(--color-white);
    border: none;
    border-radius: 0;
    padding: var(--space-sm);
    cursor: pointer;
    display: none;
}

@media (max-width: 768px) {
    .sidebar-toggle {
        display: block;
    }
}

/* 工具提示 */
/* 移除工具提示 */

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--space-3xl);
    color: var(--color-dark-gray);
}

.empty-state svg {
    width: 64px;
    height: 64px;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-sm);
    color: var(--color-primary);
}

.empty-state p {
    font-size: var(--text-sm);
    margin-bottom: var(--space-lg);
}

/* 成功/错误消息 */
.alert {
    padding: var(--space-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-lg);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--color-success);
    color: var(--color-success);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--color-warning);
    color: var(--color-warning);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: var(--color-danger);
    color: var(--color-danger);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: var(--color-info);
    color: var(--color-info);
} 

/* 批量操作工具栏 - 优化 */
.batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
    padding: var(--space-sm) var(--space-md);
    background-color: var(--color-light-gray);
    border-radius: var(--radius-sm);
    border: 1px solid var(--color-medium-gray);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.batch-info {
    margin-left: var(--space-md);
    color: var(--color-dark-gray);
    font-size: var(--text-xs);
}

/* 表格操作按钮 */
.table .actions {
    white-space: nowrap;
}

.table .actions .btn {
    margin-right: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    border-radius: var(--radius-sm);
}

.table .actions .btn:last-child {
    margin-right: 0;
}

/* 邀请码显示 */
.code-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    letter-spacing: 1px;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* 复选框样式 */
.code-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.code-checkbox:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 提示消息动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 300px;
    word-wrap: break-word;
}

.toast-success {
    background-color: #28a745;
}

.toast-error {
    background-color: #dc3545;
}

.toast-warning {
    background-color: #ffc107;
    color: #212529;
}

.toast-info {
    background-color: #17a2b8;
}

/* 卡片头部动作按钮 */
.card-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 按钮尺寸优化 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* 表格响应式优化 */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
    }
    
    .batch-toolbar {
        flex-direction: column;
        gap: 10px;
    }
    
    .batch-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .batch-info {
        margin-left: 0;
        text-align: center;
    }
}

/* 禁用状态的按钮样式 - 黑白风格 */
.btn:disabled {
    background-color: var(--color-light-gray);
    color: var(--color-dark-gray);
    border: 1px solid var(--color-dark-gray);
    cursor: not-allowed;
}

/* 加载状态优化 */
.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态徽章优化 */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: var(--color-white);
    background-color: var(--color-success);
}

.badge-warning {
    color: var(--color-black);
    background-color: var(--color-warning);
}

.badge-danger {
    color: var(--color-white);
    background-color: var(--color-danger);
} 

/* 批量生成邀请码样式 */
.form-row {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.form-row .form-group {
    flex: 1;
}

.form-text {
    font-size: var(--text-sm);
    color: var(--color-dark-gray);
    margin-top: var(--space-xs);
}

.batch-result {
    margin-top: var(--space-lg);
    padding: var(--space-lg);
    background-color: var(--color-white);
    border-radius: 0;
    border: 1px solid var(--color-black);
}

.batch-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 1px solid var(--color-black);
}

.batch-result-header h4 {
    margin: 0;
    color: var(--color-black);
    font-size: var(--text-lg);
    font-weight: 600;
}

.batch-result-actions {
    display: flex;
    gap: var(--space-sm);
}

.batch-result-summary {
    margin-bottom: var(--space-md);
    padding: var(--space-sm);
    background-color: var(--color-black);
    color: var(--color-white);
    border-radius: 0;
    font-weight: 600;
}

.batch-codes-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--color-black);
    border-radius: 0;
    background-color: var(--color-white);
}

.batch-codes-list {
    padding: var(--space-sm);
}

.batch-code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm);
    margin-bottom: var(--space-xs);
    background-color: var(--color-white);
    border-radius: 0;
    border: 1px solid var(--color-black);
}

.batch-code-item:last-child {
    margin-bottom: 0;
}

.batch-code-text {
    font-family: var(--font-mono);
    font-weight: 600;
    letter-spacing: 1px;
    color: var(--color-black);
}

.batch-code-item .btn {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
}

/* 加载状态样式 */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125em;
}

@keyframes spinner-border {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: var(--space-sm);
    }
    
    .batch-result-header {
        flex-direction: column;
        gap: var(--space-sm);
        align-items: flex-start;
    }
    
    .batch-result-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .batch-code-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-xs);
    }
    
    .batch-code-item .btn {
        align-self: flex-end;
    }
}

/* 按钮outline样式 */
.btn-outline-primary {
    color: var(--color-primary);
    border-color: var(--color-primary);
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.btn-outline-primary:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.25);
}

/* 表单提示文本样式 */
.text-muted {
    color: var(--color-dark-gray);
} 