<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化Dashboard演示 - 邀请码核销系统</title>
    <link rel="stylesheet" href="static/css/modern-console.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 移动端侧边栏切换按钮 -->
    <button class="sidebar-toggle" id="sidebar-toggle">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <div class="dashboard">
        <!-- 现代化侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>邀请码核销系统</h2>
                <p>现代化管理控制台</p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="#dashboard" class="nav-link active">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="7" height="7"></rect>
                                <rect x="14" y="3" width="7" height="7"></rect>
                                <rect x="14" y="14" width="7" height="7"></rect>
                                <rect x="3" y="14" width="7" height="7"></rect>
                            </svg>
                            仪表盘
                        </a>
                    </li>
                    <li>
                        <a href="#invite-codes" class="nav-link">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                            邀请码管理
                        </a>
                    </li>
                    <li>
                        <a href="#redeem" class="nav-link">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,11 12,14 22,4"></polyline>
                                <path d="m21,3-6.5,6.5L11,7"></path>
                            </svg>
                            核销邀请码
                        </a>
                    </li>
                    <li>
                        <a href="#api-keys" class="nav-link">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                            </svg>
                            API 管理
                        </a>
                    </li>
                    <li>
                        <a href="#users" class="nav-link">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            用户管理
                        </a>
                    </li>
                    <li>
                        <a href="#profile" class="nav-link">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            个人设置
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <p>&copy; 2024 邀请码核销系统</p>
            </div>
        </div>

        <!-- 现代化主内容区 -->
        <div class="main-content">
            <!-- 现代化内容头部 -->
            <div class="content-header">
                <h1>现代化仪表盘</h1>
                <div class="user-info">
                    <span>管理员</span>
                    <button class="btn btn-outline">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        退出登录
                    </button>
                </div>
            </div>

            <!-- 现代化内容主体 -->
            <div class="content-body">
                <!-- 现代化统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card hover-lift">
                        <h3>总邀请码</h3>
                        <div class="stat-value">1,234</div>
                        <div class="stat-label">已创建的邀请码总数</div>
                    </div>
                    <div class="stat-card hover-lift">
                        <h3>已使用</h3>
                        <div class="stat-value">856</div>
                        <div class="stat-label">已核销的邀请码</div>
                    </div>
                    <div class="stat-card hover-lift">
                        <h3>未使用</h3>
                        <div class="stat-value">378</div>
                        <div class="stat-label">可用的邀请码</div>
                    </div>
                    <div class="stat-card hover-lift">
                        <h3>使用率</h3>
                        <div class="stat-value">69%</div>
                        <div class="stat-label">邀请码使用率</div>
                    </div>
                </div>

                <!-- 现代化操作卡片 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h2>快速操作</h2>
                        <div class="card-header-actions">
                            <button class="btn btn-primary">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="16"></line>
                                    <line x1="8" y1="12" x2="16" y2="12"></line>
                                </svg>
                                生成邀请码
                            </button>
                            <button class="btn btn-secondary">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20,6 9,17 4,12"></polyline>
                                </svg>
                                核销邀请码
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="demo-input">邀请码</label>
                                <input type="text" id="demo-input" class="form-control focus-ring" placeholder="请输入8位邀请码" value="DEMO1234">
                            </div>
                            <div class="form-group">
                                <label for="demo-select">状态筛选</label>
                                <select id="demo-select" class="form-control focus-ring">
                                    <option>全部状态</option>
                                    <option>未使用</option>
                                    <option>已使用</option>
                                    <option>已过期</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.35-4.35"></path>
                                </svg>
                                搜索
                            </button>
                            <button class="btn btn-outline">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 现代化数据表格 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h2>最近邀请码</h2>
                        <button class="btn btn-outline">查看全部</button>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>邀请码</th>
                                        <th>创建时间</th>
                                        <th>过期时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="code-display">ABCD1234</span></td>
                                        <td>2024-01-15 10:30</td>
                                        <td>2024-01-22 10:30</td>
                                        <td><span class="badge badge-success">未使用</span></td>
                                        <td class="actions">
                                            <button class="btn btn-sm btn-outline">复制</button>
                                            <button class="btn btn-sm btn-secondary">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="code-display">EFGH5678</span></td>
                                        <td>2024-01-15 09:15</td>
                                        <td>2024-01-22 09:15</td>
                                        <td><span class="badge badge-danger">已使用</span></td>
                                        <td class="actions">
                                            <button class="btn btn-sm btn-outline">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="code-display">IJKL9012</span></td>
                                        <td>2024-01-14 16:45</td>
                                        <td>2024-01-21 16:45</td>
                                        <td><span class="badge badge-warning">已过期</span></td>
                                        <td class="actions">
                                            <button class="btn btn-sm btn-secondary">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 现代化分页 -->
                        <div class="pagination">
                            <button class="page-btn" disabled>上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 现代化交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏切换
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
            });
            
            // 点击外部关闭侧边栏
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768 && !sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            });
            
            // 导航链接切换
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 演示Toast消息
            setTimeout(() => {
                showToast('欢迎使用现代化Dashboard！', 'success');
            }, 1000);
        });
        
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-out forwards';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
