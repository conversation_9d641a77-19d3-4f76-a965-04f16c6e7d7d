from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from sqlalchemy import desc
from datetime import datetime, timedelta
import threading
import functools

from models import db, Api<PERSON><PERSON>, InviteCode, User
from utils.helpers import api_response, bad_request, unauthorized, not_found, forbidden

# 创建API管理蓝图
api_bp = Blueprint('api', __name__)

# 创建限流器
limiter = Limiter(key_func=get_remote_address)

# 创建一个线程锁，用于防止邀请码重复核销
redeem_lock = threading.Lock()

def api_key_required(f):
    """API Key认证装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头获取API Key
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        
        if not api_key:
            return unauthorized("缺少API Key")
        
        # 验证API Key格式
        if not api_key.startswith('ag-') or len(api_key) != 19:
            return unauthorized("API Key格式错误")
        
        # 查找API Key
        key_obj = ApiKey.query.filter_by(key=api_key, is_active=True).first()
        if not key_obj:
            return unauthorized("无效的API Key")
        
        # 更新最后使用时间
        key_obj.update_last_used()
        
        # 将API Key对象传递给视图函数
        return f(key_obj, *args, **kwargs)
    
    return decorated_function

# API Key管理接口（需要JWT认证）

@api_bp.route('/keys', methods=['GET'])
@jwt_required()
def get_api_keys():
    """获取用户的API Key列表"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    # 查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 50)
    
    # 使用优化的预加载查询
    pagination = ApiKey.get_api_keys_with_counts(int(user_id), page=page, per_page=per_page)
    
    keys = pagination.items
    
    return api_response(
        data={
            'keys': [key.to_dict() for key in keys],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page
        },
        message="获取API Key列表成功"
    )

@api_bp.route('/keys', methods=['POST'])
@jwt_required()
def create_api_key():
    """创建新的API Key"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    data = request.get_json()
    
    if not data or 'name' not in data:
        return bad_request("请提供API Key名称")
    
    name = data['name'].strip()
    if not name:
        return bad_request("API Key名称不能为空")
    
    if len(name) > 128:
        return bad_request("API Key名称过长")
    
    # 检查用户API Key数量限制
    existing_keys = ApiKey.query.filter_by(user_id=int(user_id)).count()
    if existing_keys >= 10:  # 限制每个用户最多10个API Key
        return forbidden("您已达到API Key数量上限")
    
    # 创建API Key
    api_key = ApiKey.create_key(int(user_id), name)
    
    db.session.add(api_key)
    db.session.commit()
    
    return api_response(
        data=api_key.to_dict(),
        message="API Key创建成功",
        status_code=201
    )

@api_bp.route('/keys/<int:key_id>', methods=['PUT'])
@jwt_required()
def update_api_key(key_id):
    """更新API Key"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    api_key = ApiKey.query.filter_by(id=key_id, user_id=int(user_id)).first()
    if not api_key:
        return not_found("API Key不存在")
    
    data = request.get_json()
    if not data:
        return bad_request("缺少请求数据")
    
    # 更新名称
    if 'name' in data:
        name = data['name'].strip()
        if not name:
            return bad_request("API Key名称不能为空")
        if len(name) > 128:
            return bad_request("API Key名称过长")
        api_key.name = name
    
    # 更新状态
    if 'is_active' in data:
        api_key.is_active = bool(data['is_active'])
    
    db.session.commit()
    
    return api_response(
        data=api_key.to_dict(),
        message="API Key更新成功"
    )

@api_bp.route('/keys/<int:key_id>', methods=['DELETE'])
@jwt_required()
def delete_api_key(key_id):
    """删除API Key"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    api_key = ApiKey.query.filter_by(id=key_id, user_id=int(user_id)).first()
    if not api_key:
        return not_found("API Key不存在")
    
    db.session.delete(api_key)
    db.session.commit()
    
    return api_response(
        message="API Key删除成功"
    )

@api_bp.route('/keys/<int:key_id>/codes', methods=['GET'])
@jwt_required()
def get_api_key_codes(key_id):
    """获取API Key下的邀请码"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    api_key = ApiKey.query.filter_by(id=key_id, user_id=int(user_id)).first()
    if not api_key:
        return not_found("API Key不存在")
    
    # 查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    status = request.args.get('status')  # all, used, unused, expired
    
    # 构建查询
    query = InviteCode.query.filter_by(api_key_id=key_id)
    
    # 根据状态过滤
    if status == 'used':
        query = query.filter_by(is_used=True)
    elif status == 'unused':
        query = query.filter_by(is_used=False)
    elif status == 'expired':
        query = query.filter(InviteCode.expires_at < datetime.utcnow())
    
    # 分页
    pagination = query.order_by(desc(InviteCode.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    codes = pagination.items
    
    return api_response(
        data={
            'codes': [code.to_dict() for code in codes],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'api_key': api_key.to_dict()
        },
        message="获取邀请码列表成功"
    )

@api_bp.route('/keys/<int:key_id>/codes', methods=['POST'])
@jwt_required()
def create_api_key_code(key_id):
    """为API Key创建邀请码"""
    user_id = get_jwt_identity()
    user = User.query.get(int(user_id))
    
    if not user:
        return not_found("用户不存在")
    
    api_key = ApiKey.query.filter_by(id=key_id, user_id=int(user_id)).first()
    if not api_key:
        return not_found("API Key不存在")
    
    if not api_key.is_active:
        return forbidden("API Key已禁用")
    
    data = request.get_json() or {}
    expiry_days = data.get('expiry_days', current_app.config['INVITE_CODE_EXPIRY_DAYS'])
    
    # 创建邀请码
    invite = InviteCode.create_code(int(user_id), expiry_days, key_id)
    
    db.session.add(invite)
    db.session.commit()
    
    return api_response(
        data=invite.to_dict(),
        message="邀请码创建成功",
        status_code=201
    )

# 外部API接口（使用API Key认证）

@api_bp.route('/redeem', methods=['POST'])
@api_key_required
@limiter.limit("60 per minute")  # 限制每分钟60次请求
def api_redeem_code(api_key):
    """通过API核销邀请码"""
    data = request.get_json()
    
    if not data or 'code' not in data:
        return bad_request("请提供邀请码")
    
    invite_code = data['code']
    
    # 使用线程锁防止并发核销
    with redeem_lock:
        try:
            # 查找邀请码并加锁
            invite = InviteCode.query.filter_by(
                code=invite_code, 
                api_key_id=api_key.id
            ).with_for_update().first()
            
            if not invite:
                return not_found("邀请码不存在或不属于当前API Key")
            
            # 验证邀请码
            if invite.is_used:
                return bad_request("邀请码已被使用")
            
            if invite.expires_at < datetime.utcnow():
                return bad_request("邀请码已过期")
            
            # 核销邀请码（API核销不需要指定用户）
            invite.is_used = True
            invite.redeemed_at = datetime.utcnow()
            # API核销时used_by_id保持为None，表示通过API核销
            
            db.session.commit()
            
            return api_response(
                data={
                    'code': invite.code,
                    'redeemed_at': invite.redeemed_at.isoformat(),
                    'api_key_name': api_key.name
                },
                message="邀请码核销成功"
            )
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"API邀请码核销失败: {str(e)}")
            return bad_request("邀请码核销失败，请稍后重试")

@api_bp.route('/codes/<code>', methods=['GET'])
@api_key_required
@limiter.limit("120 per minute")  # 限制每分钟120次请求
def api_get_code(api_key, code):
    """通过API查询邀请码状态"""
    invite = InviteCode.query.filter_by(
        code=code, 
        api_key_id=api_key.id
    ).first()
    
    if not invite:
        return not_found("邀请码不存在或不属于当前API Key")
    
    return api_response(
        data=invite.to_dict(),
        message="获取邀请码信息成功"
    )

@api_bp.route('/codes', methods=['GET'])
@api_key_required
@limiter.limit("60 per minute")  # 限制每分钟60次请求
def api_get_codes(api_key):
    """通过API获取邀请码列表"""
    # 查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    status = request.args.get('status')  # all, used, unused, expired
    
    # 构建查询
    query = InviteCode.query.filter_by(api_key_id=api_key.id)
    
    # 根据状态过滤
    if status == 'used':
        query = query.filter_by(is_used=True)
    elif status == 'unused':
        query = query.filter_by(is_used=False)
    elif status == 'expired':
        query = query.filter(InviteCode.expires_at < datetime.utcnow())
    
    # 分页
    pagination = query.order_by(desc(InviteCode.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    codes = pagination.items
    
    return api_response(
        data={
            'codes': [code.to_dict() for code in codes],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'api_key_name': api_key.name
        },
        message="获取邀请码列表成功"
    ) 